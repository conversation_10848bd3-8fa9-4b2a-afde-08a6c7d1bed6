import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
  UseInterceptors,
  UseGuards,
  Req,
  UnauthorizedException,
  Res,
  DefaultValuePipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiConsumes,
  ApiBearerAuth,
  ApiQuery,
  ApiResponse,
} from '@nestjs/swagger';
import { CreateAssessmentDto } from './dto/creates/create-assessment.dto';
import { UpdateAssessmentDto } from './dto/updates/update-assessment.dto';
import { SaveTextFieldScoreDto } from './dto/save-textfield-score.dto';
import { Assessment } from './entities/assessment.entity';
import type { DataParams, DataResponse } from 'src/types/params';
import { AssessmentType } from './enums/assessment-type.enum';
import { AssessmentsService } from './services/assessments.service';
import { AnyFilesInterceptor } from '@nestjs/platform-express';
import { QuizDashboardService } from './services/quiz.dashboard.service';
import { EvaluateDashBoardService } from './services/evaluate.dashboard.service';
import { AuthGuard } from 'src/auth/auth.guard';
import { Permission } from 'src/auth/permission.decorator';
import { Request, Response } from 'express';
import { QuizService } from './services/quiz.service';
import { MockupDataService } from './services/mockup-data.service';
import { Submission } from './entities/submission.entity';
import { DefaultQueryParams } from 'src/utils/default-query.decorator';
import { ParticipantDetailsQueryDto } from './dto/participant-details-query.dto';
import { ItemBlock } from './entities/item-block.entity';

@ApiTags('Assessment')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('assessments')
export class AssessmentsController {
  constructor(
    private readonly assessmentsService: AssessmentsService,
    private readonly quizDashboardService: QuizDashboardService,
    private readonly evaluateDashboardService: EvaluateDashBoardService,
    private readonly quizService: QuizService,
    private readonly mockupDataService: MockupDataService,
  ) {}

  @ApiBearerAuth()
  @Permission('manage_all_surveys')
  @Permission('manage_own_surveys')
  @Post()
  @ApiOperation({ summary: 'New Assessment (Quiz/Evaluate)' })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(AnyFilesInterceptor())
  create(@Body() dto: CreateAssessmentDto) {
    console.log('Create Assessment DTO:', dto);
    return this.assessmentsService.createOne(dto);
  }

  @ApiBearerAuth()
  @Permission('manage_all_surveys')
  @Get()
  @DefaultQueryParams()
  @ApiQuery({
    name: 'type',
    required: true,
    enum: AssessmentType,
    description: 'ประเภทของแบบประเมิน',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'ค้นหาด้วยชื่อแบบประเมิน',
  })
  async getAll(
    @Query() query: DataParams,
    @Query('type') type: AssessmentType,
  ): Promise<DataResponse<Assessment>> {
    return this.assessmentsService.getAll(query, type);
  }

  @Get('prototypes')
  @ApiOperation({
    summary: 'Get all assessments that are prototypes (isPrototype=true)',
  })
  @DefaultQueryParams()
  @ApiQuery({ name: 'type', required: true, enum: AssessmentType })
  async getAllPrototypes(
    @Query('type') type: AssessmentType,
    @Query() query: DataParams,
  ): Promise<DataResponse<Assessment>> {
    return this.assessmentsService.getAllPrototypes(type, query);
  }

  // @Get(':id')
  // @ApiOperation({ summary: 'Get a single assessment by ID' })
  // @ApiParam({
  //   name: 'id',
  //   description: 'The ID of the assessment',
  //   type: Number,
  //   example: 1,
  // })
  // async findOne(@Param('id', ParseIntPipe) id: number): Promise<Assessment> {
  //   return this.assessmentsService.findOne(id);
  // }

  @Get(':id')
@ApiOperation({ summary: 'Get a single assessment by ID with paginated itemBlocks' })
@ApiParam({
  name: 'id',
  description: 'The ID of the assessment',
  type: Number,
  example: 1,
})
@ApiQuery({ name: 'page', required: false, type: Number, example: 1 })
async findOne(
  @Param('id', ParseIntPipe) id: number,
  @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
): Promise<{ assessment: Assessment; pagedItemBlocks: ItemBlock[] }> {
  const FIXED_LIMIT = 10;
  
  return this.assessmentsService.findOne(id, { page, limit: FIXED_LIMIT });
}


  @Get('/preview/:id/:section')
  @ApiOperation({ summary: 'Get assessment by section' })
  getOne(@Param('id') id: number, @Param('section') section: number) {
    return this.assessmentsService.findOneBySection(+id, +section);
  }
  @ApiBearerAuth()
  @Permission('manage_own_surveys')
  @Patch(':id')
  @ApiOperation({ summary: 'Update an existing assessment' })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(AnyFilesInterceptor())
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateAssessmentDto: UpdateAssessmentDto,
  ): Promise<Assessment> {
    return this.assessmentsService.update(id, updateAssessmentDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete an assessment' })
  remove(@Param('id') id: string) {
    return this.assessmentsService.remove(+id);
  }

  @Get('dashboard/quiz/:id')
  @ApiOperation({ summary: 'ดึงข้อมูลแดชบอร์ดของแบบทดสอบ' })
  @ApiParam({ name: 'id', description: 'รหัสของแบบทดสอบ', type: Number })
  async getQuizDashboardMeta(@Param('id', ParseIntPipe) id: number) {
    return this.quizDashboardService.generateAssessmentMeta(id);
  }

  // @Get('dashboard/quiz/:id/score-distribution')
  // @ApiOperation({ summary: 'ดึงข้อมูลกราฟการกระจายคะแนนของแบบทดสอบ' })
  // @ApiParam({ name: 'id', description: 'รหัสของแบบทดสอบ', type: Number })
  // async getQuizScoreDistribution(@Param('id', ParseIntPipe) id: number) {
  //   return this.quizDashboardService.generateScoreDistributionChart(id);
  // }

  @Get('dashboard/quiz/:id/questions')
  @ApiOperation({ summary: 'ดึงข้อมูลการตอบคำถามทั้งหมดของแบบทดสอบ' })
  @DefaultQueryParams()
  @ApiParam({ name: 'id', description: 'รหัสของแบบทดสอบ', type: Number })
  async getQuizQuestionResponses(
    @Param('id', ParseIntPipe) id: number,
    @Query() query: DataParams,
  ) {
    return this.quizDashboardService.generateAllQuestionResponses(id, query);
  }

  @Get('dashboard/quiz/:id/participants')
  @ApiOperation({ summary: 'ดึงข้อมูลผู้เข้าร่วมทำแบบทดสอบทั้งหมด' })
  @ApiParam({ name: 'id', description: 'รหัสของแบบทดสอบ', type: Number })
  async getQuizParticipants(
    @Param('id', ParseIntPipe) id: number,
    @Query() query: DataParams,
  ) {
    return this.quizDashboardService.getAllParticipants(id, query);
  }
  @Get('dashboard/quiz/participant/:id')
  @ApiOperation({
    summary: 'ดึงข้อมูลรายละเอียดของผู้เข้าร่วมทำแบบทดสอบเฉพาะราย',
  })
  @ApiParam({ name: 'id', description: 'รหัสของผู้เข้าร่วม', type: Number })
  @DefaultQueryParams()
  async getQuizParticipantDetails(
    @Param('id', ParseIntPipe) id: number,
    @Query() query: DataParams,
  ) {
    return this.quizDashboardService.getOneParticipant(id, query);
  }

  @Get('dashboard/quiz/participant/:id/textfield-grading')
  @ApiOperation({
    summary:
      'ดึงข้อมูลรายละเอียดของผู้เข้าร่วมทำแบบทดสอบสำหรับการให้คะแนน TEXTFIELD',
  })
  @ApiParam({ name: 'id', description: 'รหัสของผู้เข้าร่วม', type: Number })
  @DefaultQueryParams()
  async getQuizParticipantTextFieldGrading(
    @Param('id', ParseIntPipe) id: number,
    @Query() query: DataParams,
  ) {
    return this.quizDashboardService.getOneParticipantTextFieldGrading(
      id,
      query,
    );
  }

  @Post('dashboard/quiz/participant/:submissionId/textfield-score')
  @ApiOperation({ summary: 'Save custom score for TEXTFIELD question' })
  @ApiParam({ name: 'submissionId', description: 'Submission ID' })
  @ApiResponse({
    status: 200,
    description: 'Score saved successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        message: { type: 'string' },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'Submission or question not found' })
  async saveTextFieldScore(
    @Param('submissionId', ParseIntPipe) submissionId: number,
    @Body() saveScoreDto: SaveTextFieldScoreDto,
  ) {
    return this.quizDashboardService.saveTextFieldScore(
      submissionId,
      saveScoreDto.questionId,
      saveScoreDto.score,
    );
  }

  @Get('dashboard/evaluate/:assessmentId')
  @ApiOperation({ summary: 'ดึงข่อมูลสำหรับโชว์ Graph' })
  async getChartData(
    @Param('assessmentId', ParseIntPipe) assessmentId: number,
  ) {
    return this.evaluateDashboardService.getChartData(assessmentId);
  }

  @Get(':id/export/excel')
  @ApiOperation({ summary: 'Export assessment questions to Excel file' })
  @ApiParam({
    name: 'id',
    description: 'Assessment ID to export',
    type: Number,
  })
  async exportAssessmentToExcel(
    @Param('id', ParseIntPipe) id: number,
    @Res({ passthrough: false }) res: Response,
  ): Promise<void> {
    return this.evaluateDashboardService.exportAssessmentToExcel(id, res);
  }

  @Get('/header/:assessmentId')
  @ApiOperation({ summary: 'ดึงข่อมูลจำนวนของResponses ทั้งหมด' })
  async getNumberOfResponses(
    @Param('assessmentId', ParseIntPipe) assessmentId: number,
  ): Promise<number> {
    return this.evaluateDashboardService.getNumberOfResponses(assessmentId);
  }
  @Patch(':sourceId/copy-to/:targetId')
  @ApiOperation({ summary: 'Copy content from one assessment to another' })
  @ApiParam({
    name: 'sourceId',
    description: 'Source assessment ID',
    type: Number,
  })
  @ApiParam({
    name: 'targetId',
    description: 'Target assessment ID',
    type: Number,
  })
  async copyAssessmentContent(
    @Param('sourceId', ParseIntPipe) sourceId: number,
    @Param('targetId', ParseIntPipe) targetId: number,
  ): Promise<Assessment> {
    return this.assessmentsService.duplicateAssessmentTemplate(
      sourceId,
      targetId,
    );
  }

  @Permission('manage_own_surveys')
  @Get('editor/view-assessment')
  @DefaultQueryParams()
  @ApiQuery({ name: 'type', required: false, enum: AssessmentType })
  async getAllEditor(
    @Query() query: DataParams,
    @Query('type') type: AssessmentType,
    @Req() request: Request,
  ): Promise<DataResponse<Assessment>> {
    const user = request['user'];
    if (!user) {
      throw new UnauthorizedException('User not found');
    }
    return this.assessmentsService.getAllEditor(query, type, user);
  }

  @Permission('take_public_surveys')
  @Get('standardUser/view-assessment')
  @DefaultQueryParams()
  @ApiQuery({ name: 'type', required: false, enum: AssessmentType })
  async getAllUser(
    @Query() query: DataParams,
    @Query('type') type: AssessmentType,
  ): Promise<DataResponse<Assessment>> {
    return this.assessmentsService.getAllForUserStandard(query, type);
  }

  @Get('url/:url')
  @ApiOperation({ summary: 'Get assessment by URL' })
  async getByURL(@Param('url') uuid: string) {
    return this.assessmentsService.getByURL(uuid);
  }

  @Post('mockup')
  @ApiOperation({
    summary: 'Create Mockup Assessment Data',
    description:
      'Creates a complete assessment with 100 questions (25 questions each for RADIO, CHECKBOX, TEXTFIELD, and UPLOAD types)',
  })
  async createMockupAssessment(): Promise<Assessment> {
    return this.mockupDataService.createMockupQuizAssessment();
  }

  @Post('mockup/submissions/:id')
  @ApiOperation({
    summary: 'Create Mockup Submissions for Assessment',
    description:
      'Creates 50 mockup submissions with realistic response patterns for the specified assessment',
  })
  @ApiParam({
    name: 'id',
    description: 'Assessment ID to create submissions for',
    type: Number,
  })
  async createMockupSubmissions(
    @Param('id', ParseIntPipe) assessmentId: number,
  ): Promise<Submission[]> {
    return this.mockupDataService.createMockupSubmissions(assessmentId, 50);
  }

  @Post('mockup/complete')
  @ApiOperation({
    summary: 'Create Complete Mockup Data (Assessment + Submissions)',
    description:
      'Creates a complete assessment with 100 questions and 50 submissions for dashboard testing',
  })
  async createCompleteAssessmentMockup(): Promise<{
    assessment: Assessment;
    submissions: Submission[];
  }> {
    return this.mockupDataService.createCompleteAssessmentWithSubmissions(50);
  }
  @Get('header-with-submissions/url/:linkUrl')
  @ApiOperation({ summary: 'Get quiz header with user submission history' })
  async getQuizHeaderWithUserSubmissions(
    @Param('linkUrl') linkUrl: string,
    @Query('userId') userId?: number,
  ) {
    return this.quizService.getQuizHeaderWithUserSubmissions(linkUrl, userId);
  }
}
